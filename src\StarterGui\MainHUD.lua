-- MainHUD.lua
-- HUD principal mostrando vida, BaseSize e BuildingMaterials

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local updateBaseInfo = remoteEvents:WaitForChild("UpdateBaseInfo")

-- Variáveis da UI
local screenGui = nil
local healthBar = nil
local healthText = nil
local baseBar = nil
local baseText = nil
local materialsText = nil

-- Função para criar o HUD
local function createHUD()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove HUD existente
    local existingHUD = playerGui:FindFirstChild("MainHUD")
    if existingHUD then existingHUD:Destroy() end
    
    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MainHUD"
    screenGui.Parent = playerGui
    
    -- Frame principal do HUD
    local hudFrame = Instance.new("Frame")
    hudFrame.Name = "HUDFrame"
    hudFrame.Size = UDim2.new(0, 300, 0, 120)
    hudFrame.Position = UDim2.new(0, 10, 0, 10)
    hudFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    hudFrame.BackgroundTransparency = 0.3
    hudFrame.BorderSizePixel = 2
    hudFrame.BorderColor3 = Color3.new(1, 1, 1)
    hudFrame.Parent = screenGui
    
    -- === BARRA DE VIDA ===
    local healthLabel = Instance.new("TextLabel")
    healthLabel.Size = UDim2.new(1, 0, 0, 20)
    healthLabel.Position = UDim2.new(0, 0, 0, 5)
    healthLabel.BackgroundTransparency = 1
    healthLabel.Text = "❤️ VIDA"
    healthLabel.TextColor3 = Color3.new(1, 1, 1)
    healthLabel.TextScaled = true
    healthLabel.Font = Enum.Font.SourceSansBold
    healthLabel.Parent = hudFrame
    
    -- Background da barra de vida
    local healthBarBG = Instance.new("Frame")
    healthBarBG.Size = UDim2.new(1, -10, 0, 15)
    healthBarBG.Position = UDim2.new(0, 5, 0, 25)
    healthBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    healthBarBG.BorderSizePixel = 1
    healthBarBG.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    healthBarBG.Parent = hudFrame
    
    -- Barra de vida
    healthBar = Instance.new("Frame")
    healthBar.Size = UDim2.new(1, 0, 1, 0)
    healthBar.Position = UDim2.new(0, 0, 0, 0)
    healthBar.BackgroundColor3 = Color3.new(1, 0, 0)
    healthBar.BorderSizePixel = 0
    healthBar.Parent = healthBarBG
    
    -- Texto da vida
    healthText = Instance.new("TextLabel")
    healthText.Size = UDim2.new(1, 0, 1, 0)
    healthText.Position = UDim2.new(0, 0, 0, 0)
    healthText.BackgroundTransparency = 1
    healthText.Text = "100/100"
    healthText.TextColor3 = Color3.new(1, 1, 1)
    healthText.TextScaled = true
    healthText.Font = Enum.Font.SourceSansBold
    healthText.Parent = healthBarBG
    
    -- === BARRA DA BASE ===
    local baseLabel = Instance.new("TextLabel")
    baseLabel.Size = UDim2.new(1, 0, 0, 20)
    baseLabel.Position = UDim2.new(0, 0, 0, 45)
    baseLabel.BackgroundTransparency = 1
    baseLabel.Text = "🏠 BASE"
    baseLabel.TextColor3 = Color3.new(1, 1, 1)
    baseLabel.TextScaled = true
    baseLabel.Font = Enum.Font.SourceSansBold
    baseLabel.Parent = hudFrame
    
    -- Background da barra da base
    local baseBarBG = Instance.new("Frame")
    baseBarBG.Size = UDim2.new(1, -10, 0, 15)
    baseBarBG.Position = UDim2.new(0, 5, 0, 65)
    baseBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    baseBarBG.BorderSizePixel = 1
    baseBarBG.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    baseBarBG.Parent = hudFrame
    
    -- Barra da base
    baseBar = Instance.new("Frame")
    baseBar.Size = UDim2.new(0.2, 0, 1, 0) -- Começa com 20% (100/500)
    baseBar.Position = UDim2.new(0, 0, 0, 0)
    baseBar.BackgroundColor3 = Color3.new(0, 1, 0)
    baseBar.BorderSizePixel = 0
    baseBar.Parent = baseBarBG
    
    -- Texto da base
    baseText = Instance.new("TextLabel")
    baseText.Size = UDim2.new(1, 0, 1, 0)
    baseText.Position = UDim2.new(0, 0, 0, 0)
    baseText.BackgroundTransparency = 1
    baseText.Text = "Nenhuma Base"
    baseText.TextColor3 = Color3.new(1, 1, 1)
    baseText.TextScaled = true
    baseText.Font = Enum.Font.SourceSansBold
    baseText.Parent = baseBarBG
    
    -- === MATERIAIS DE CONSTRUÇÃO ===
    local materialsFrame = Instance.new("Frame")
    materialsFrame.Size = UDim2.new(1, -10, 0, 25)
    materialsFrame.Position = UDim2.new(0, 5, 0, 85)
    materialsFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    materialsFrame.BorderSizePixel = 1
    materialsFrame.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    materialsFrame.Parent = hudFrame
    
    -- Ícone de materiais
    local materialsIcon = Instance.new("TextLabel")
    materialsIcon.Size = UDim2.new(0, 25, 1, 0)
    materialsIcon.Position = UDim2.new(0, 0, 0, 0)
    materialsIcon.BackgroundTransparency = 1
    materialsIcon.Text = "🔧"
    materialsIcon.TextColor3 = Color3.new(1, 1, 1)
    materialsIcon.TextScaled = true
    materialsIcon.Font = Enum.Font.SourceSansBold
    materialsIcon.Parent = materialsFrame
    
    -- Texto dos materiais
    materialsText = Instance.new("TextLabel")
    materialsText.Size = UDim2.new(1, -30, 1, 0)
    materialsText.Position = UDim2.new(0, 30, 0, 0)
    materialsText.BackgroundTransparency = 1
    materialsText.Text = "Materiais: 0"
    materialsText.TextColor3 = Color3.new(1, 1, 1)
    materialsText.TextScaled = true
    materialsText.Font = Enum.Font.SourceSansBold
    materialsText.TextXAlignment = Enum.TextXAlignment.Left
    materialsText.Parent = materialsFrame
end

-- Função para atualizar a vida
local function updateHealth()
    if not player.Character or not player.Character:FindFirstChild("Humanoid") then
        return
    end
    
    local humanoid = player.Character.Humanoid
    local health = math.floor(humanoid.Health)
    local maxHealth = math.floor(humanoid.MaxHealth)
    local healthRatio = health / maxHealth
    
    -- Atualiza texto
    healthText.Text = health .. "/" .. maxHealth
    
    -- Atualiza barra com animação
    local targetSize = UDim2.new(healthRatio, 0, 1, 0)
    local tween = TweenService:Create(healthBar, TweenInfo.new(0.3), {Size = targetSize})
    tween:Play()
    
    -- Muda cor baseada na vida
    if healthRatio > 0.6 then
        healthBar.BackgroundColor3 = Color3.new(0, 1, 0) -- Verde
    elseif healthRatio > 0.3 then
        healthBar.BackgroundColor3 = Color3.new(1, 1, 0) -- Amarelo
    else
        healthBar.BackgroundColor3 = Color3.new(1, 0, 0) -- Vermelho
    end
end

-- Função para atualizar informações da base
local function updateBaseInfo(baseName, baseSize, buildingMaterials)
    if baseName then
        -- Tem base
        local maxBaseSize = 500
        local baseRatio = baseSize / maxBaseSize
        
        -- Atualiza texto da base
        baseText.Text = baseName .. ": " .. baseSize .. "/" .. maxBaseSize
        
        -- Atualiza barra da base com animação
        local targetSize = UDim2.new(baseRatio, 0, 1, 0)
        local tween = TweenService:Create(baseBar, TweenInfo.new(0.5), {Size = targetSize})
        tween:Play()
        
        -- Muda cor baseada no tamanho
        if baseRatio > 0.6 then
            baseBar.BackgroundColor3 = Color3.new(0, 1, 0) -- Verde
        elseif baseRatio > 0.3 then
            baseBar.BackgroundColor3 = Color3.new(1, 1, 0) -- Amarelo
        else
            baseBar.BackgroundColor3 = Color3.new(1, 0, 0) -- Vermelho
        end
        
        -- Atualiza materiais
        materialsText.Text = "Materiais: " .. buildingMaterials
    else
        -- Não tem base
        baseText.Text = "Nenhuma Base"
        baseBar.Size = UDim2.new(0, 0, 1, 0)
        materialsText.Text = "Materiais: 0"
    end
end

-- Função para verificar base do jogador
local function checkPlayerBase()
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if (owner and owner.Value == player) or (partner and partner.Value == player) then
                local baseSizeValue = base:FindFirstChild("BaseSize")
                local buildingMaterialsValue = base:FindFirstChild("BuildingMaterials")
                
                if baseSizeValue and buildingMaterialsValue then
                    updateBaseInfo(base.Name, baseSizeValue.Value, buildingMaterialsValue.Value)
                    return
                end
            end
        end
    end
    
    -- Não encontrou base
    updateBaseInfo(nil, 0, 0)
end

-- Inicializa o HUD
local function initializeHUD()
    createHUD()
    
    -- Loop de atualização
    local connection
    connection = RunService.Heartbeat:Connect(function()
        if not player.Character then
            connection:Disconnect()
            return
        end
        
        updateHealth()
        checkPlayerBase()
    end)
end

-- Conecta evento de atualização da base
updateBaseInfo.OnClientEvent:Connect(function(baseName, baseSize, buildingMaterials)
    updateBaseInfo(baseName, baseSize, buildingMaterials)
end)

-- Inicializa quando o jogador spawna
if player.Character then
    initializeHUD()
end

player.CharacterAdded:Connect(function()
    wait(1)
    initializeHUD()
end)

print("MainHUD carregado com sucesso!")