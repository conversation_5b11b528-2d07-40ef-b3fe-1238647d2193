-- CollectorGunScript.client.lua
-- LocalScript para a CollectorGun

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local startCollecting = remoteEvents:WaitForChild("StartCollecting")
local stopCollecting = remoteEvents:WaitForChild("StopCollecting")
local attackBase = remoteEvents:WaitForChild("AttackBase")

-- Configurações da ferramenta
local RANGE = 150
local SHRINK_RATE = 0.1 -- Taxa de encolhimento por frame
local MIN_SIZE_MULTIPLIER = 0.1 -- <PERSON><PERSON><PERSON> (10% do original)

-- <PERSON><PERSON> de uso
local tool = script.Parent
local isCollecting = false
local currentTarget = nil
local collectConnection = nil

-- Função para criar efeito visual do raio
local function createBeamEffect(startPos, endPos, color)
    local beam = Instance.new("Part")
    beam.Name = "CollectorBeam"
    beam.Size = Vector3.new(0.2, 0.2, (endPos - startPos).Magnitude)
    beam.BrickColor = color or BrickColor.new("Bright blue")
    beam.Material = Enum.Material.Neon
    beam.CanCollide = false
    beam.Anchored = true
    beam.Parent = workspace
    
    -- Posiciona o raio
    beam.CFrame = CFrame.lookAt(startPos, endPos) * CFrame.new(0, 0, -beam.Size.Z / 2)
    
    -- Efeito de fade
    local transparency = 0
    local connection
    connection = RunService.Heartbeat:Connect(function()
        transparency = transparency + 0.1
        beam.Transparency = transparency
        
        if transparency >= 1 then
            connection:Disconnect()
            beam:Destroy()
        end
    end)
    
    return beam
end

-- Função para verificar se um objeto é coletável
local function isCollectable(obj)
    return obj:FindFirstChild("OriginalSize") and obj.Parent == workspace
end

-- Função para verificar se um objeto é uma base inimiga
local function isEnemyBase(obj)
    local baseModel = obj.Parent
    if baseModel and baseModel:FindFirstChild("Owner") and baseModel:FindFirstChild("BaseSize") then
        local owner = baseModel.Owner.Value
        return owner and owner ~= player
    end
    return false
end

-- Função para iniciar coleta/ataque
local function startCollectingResource()
    isCollecting = true
    
    collectConnection = RunService.Heartbeat:Connect(function()
        if not isCollecting then
            collectConnection:Disconnect()
            return
        end
        
        -- Raycast para detectar alvo
        local character = player.Character
        if not character then return end
        
        local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
        if not humanoidRootPart then return end
        
        -- Posição de origem
        local startPos = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 2
        local direction = (mouse.Hit.Position - startPos).Unit
        
        -- Raycast
        local raycastParams = RaycastParams.new()
        raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
        raycastParams.FilterDescendantsInstances = {character}
        
        local raycastResult = workspace:Raycast(startPos, direction * RANGE, raycastParams)
        
        if raycastResult then
            local hitObject = raycastResult.Instance
            local endPos = raycastResult.Position
            
            -- Verifica se é um recurso coletável
            if isCollectable(hitObject) then
                currentTarget = hitObject
                
                -- Cria efeito visual
                createBeamEffect(startPos, endPos, BrickColor.new("Bright blue"))
                
                -- Envia evento para o servidor
                startCollecting:FireServer(hitObject)
                
            -- Verifica se é uma base inimiga
            elseif isEnemyBase(hitObject) then
                currentTarget = hitObject
                
                -- Cria efeito visual vermelho para ataque
                createBeamEffect(startPos, endPos, BrickColor.new("Bright red"))
                
                -- Envia evento de ataque à base
                attackBase:FireServer(hitObject.Parent)
                
            else
                currentTarget = nil
            end
        else
            currentTarget = nil
        end
    end)
end

-- Função para parar coleta/ataque
local function stopCollectingResource()
    isCollecting = false
    
    if collectConnection then
        collectConnection:Disconnect()
        collectConnection = nil
    end
    
    -- Se estava coletando um recurso, para o encolhimento
    if currentTarget and isCollectable(currentTarget) then
        stopCollecting:FireServer(currentTarget)
    end
    
    currentTarget = nil
end

-- Conecta eventos da ferramenta
tool.Activated:Connect(startCollectingResource)
tool.Deactivated:Connect(stopCollectingResource)

tool.Equipped:Connect(function()
    mouse.Icon = "rbxasset://textures/ArrowCursor.png"
end)

tool.Unequipped:Connect(function()
    stopCollectingResource()
    mouse.Icon = ""
end)