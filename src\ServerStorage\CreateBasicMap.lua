-- CreateBasicMap.lua
-- Cria o mapa básico para o jogo

local Lighting = game:GetService("Lighting")

local function createBasicMap()
    -- Remove objetos padrão se existirem
    local existingBaseplate = workspace:FindFirstChild("Baseplate")
    if existingBaseplate then existingBaseplate:Destroy() end
    
    -- Cria baseplate grande
    local baseplate = Instance.new("Part")
    baseplate.Name = "Baseplate"
    baseplate.Size = Vector3.new(1000, 4, 1000)
    baseplate.Position = Vector3.new(0, -2, 0)
    baseplate.BrickColor = BrickColor.new("Bright green")
    baseplate.Material = Enum.Material.Grass
    baseplate.Anchored = true
    baseplate.Parent = workspace
    
    -- Cria spawn central temporário (será removido pelo SpawnManager)
    local centralSpawn = Instance.new("SpawnLocation")
    centralSpawn.Name = "CentralSpawn"
    centralSpawn.Size = Vector3.new(10, 1, 10)
    centralSpawn.Position = Vector3.new(0, 1, 0)
    centralSpawn.BrickColor = BrickColor.new("Bright blue")
    centralSpawn.Material = Enum.Material.Neon
    centralSpawn.Anchored = true
    centralSpawn.Enabled = false -- Desabilitado por padrão
    centralSpawn.Parent = workspace
    
    -- Adiciona texto ao spawn central
    local surfaceGui = Instance.new("SurfaceGui")
    surfaceGui.Face = Enum.NormalId.Top
    surfaceGui.Parent = centralSpawn
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "ARENA CENTRAL (BACKUP)"
    textLabel.TextColor3 = Color3.new(1, 1, 1)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = surfaceGui
    
    -- Cria decorações nos cantos
    local decorationPositions = {
        Vector3.new(400, 5, 400),
        Vector3.new(-400, 5, 400),
        Vector3.new(400, 5, -400),
        Vector3.new(-400, 5, -400)
    }
    
    for i, position in ipairs(decorationPositions) do
        local decoration = Instance.new("Part")
        decoration.Name = "Decoration_" .. i
        decoration.Size = Vector3.new(20, 10, 20)
        decoration.Position = position
        decoration.BrickColor = BrickColor.new("Dark stone grey")
        decoration.Material = Enum.Material.Rock
        decoration.Shape = Enum.PartType.Cylinder
        decoration.Anchored = true
        decoration.Parent = workspace
    end
    
    -- Configura iluminação
    Lighting.Brightness = 2
    Lighting.Ambient = Color3.new(0.5, 0.5, 0.5)
    Lighting.ColorShift_Bottom = Color3.new(0, 0, 0)
    Lighting.ColorShift_Top = Color3.new(0, 0, 0)
    Lighting.ShadowSoftness = 0.2
    Lighting.ClockTime = 12
    
    print("Mapa básico criado com sucesso!")
    print("- Baseplate: 1000x1000 studs")
    print("- Spawn central temporário")
    print("- 4 decorações nos cantos")
    print("- Iluminação configurada")
    
    return true
end

-- Executa a criação
createBasicMap()

return true