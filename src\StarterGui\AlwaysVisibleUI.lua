-- AlwaysVisibleUI.lua
-- UI que sempre aparece para o jogador

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer

-- Função para criar UI sempre visível
local function createAlwaysVisibleUI()
    local playerGui = player:Wait<PERSON><PERSON><PERSON>hil<PERSON>("PlayerGui")
    
    -- Remove UI existente
    local existingUI = playerGui:FindFirstChild("AlwaysVisibleUI")
    if existingUI then existingUI:Destroy() end
    
    -- Cria ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "AlwaysVisibleUI"
    screenGui.Parent = playerGui
    
    -- Frame de status sempre visível
    local statusFrame = Instance.new("Frame")
    statusFrame.Name = "StatusFrame"
    statusFrame.Size = UDim2.new(0, 250, 0, 100)
    statusFrame.Position = UDim2.new(0, 10, 0, 10)
    statusFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    statusFrame.BackgroundTransparency = 0.3
    statusFrame.BorderSizePixel = 2
    statusFrame.BorderColor3 = Color3.new(1, 1, 1)
    statusFrame.Parent = screenGui
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 25)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "🎮 ARENA GAME"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = statusFrame
    
    -- Vida
    local healthLabel = Instance.new("TextLabel")
    healthLabel.Name = "HealthLabel"
    healthLabel.Size = UDim2.new(1, 0, 0, 20)
    healthLabel.Position = UDim2.new(0, 0, 0, 25)
    healthLabel.BackgroundTransparency = 1
    healthLabel.Text = "❤️ Vida: 100/100"
    healthLabel.TextColor3 = Color3.new(1, 0, 0)
    healthLabel.TextScaled = true
    healthLabel.Font = Enum.Font.SourceSans
    healthLabel.Parent = statusFrame
    
    -- Base
    local baseLabel = Instance.new("TextLabel")
    baseLabel.Name = "BaseLabel"
    baseLabel.Size = UDim2.new(1, 0, 0, 20)
    baseLabel.Position = UDim2.new(0, 0, 0, 45)
    baseLabel.BackgroundTransparency = 1
    baseLabel.Text = "🏠 Base: Nenhuma"
    baseLabel.TextColor3 = Color3.new(0, 1, 0)
    baseLabel.TextScaled = true
    baseLabel.Font = Enum.Font.SourceSans
    baseLabel.Parent = statusFrame
    
    -- Instruções
    local instructionsLabel = Instance.new("TextLabel")
    instructionsLabel.Size = UDim2.new(1, 0, 0, 30)
    instructionsLabel.Position = UDim2.new(0, 0, 0, 65)
    instructionsLabel.BackgroundTransparency = 1
    instructionsLabel.Text = "TAB = Menu | Toque ClaimPad = Reivindicar"
    instructionsLabel.TextColor3 = Color3.new(1, 1, 1)
    instructionsLabel.TextScaled = true
    instructionsLabel.Font = Enum.Font.SourceSans
    instructionsLabel.Parent = statusFrame
    
    -- Frame de ferramentas
    local toolsFrame = Instance.new("Frame")
    toolsFrame.Name = "ToolsFrame"
    toolsFrame.Size = UDim2.new(0, 200, 0, 80)
    toolsFrame.Position = UDim2.new(1, -210, 0, 10)
    toolsFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    toolsFrame.BackgroundTransparency = 0.3
    toolsFrame.BorderSizePixel = 2
    toolsFrame.BorderColor3 = Color3.new(1, 1, 1)
    toolsFrame.Parent = screenGui
    
    -- Título das ferramentas
    local toolsTitle = Instance.new("TextLabel")
    toolsTitle.Size = UDim2.new(1, 0, 0, 25)
    toolsTitle.Position = UDim2.new(0, 0, 0, 0)
    toolsTitle.BackgroundTransparency = 1
    toolsTitle.Text = "🔧 FERRAMENTAS"
    toolsTitle.TextColor3 = Color3.new(1, 1, 1)
    toolsTitle.TextScaled = true
    toolsTitle.Font = Enum.Font.SourceSansBold
    toolsTitle.Parent = toolsFrame
    
    -- Status das ferramentas
    local toolsStatus = Instance.new("TextLabel")
    toolsStatus.Name = "ToolsStatus"
    toolsStatus.Size = UDim2.new(1, 0, 1, -25)
    toolsStatus.Position = UDim2.new(0, 0, 0, 25)
    toolsStatus.BackgroundTransparency = 1
    toolsStatus.Text = "🔫 CombatGun: Disponível\n🔨 CollectorGun: Disponível"
    toolsStatus.TextColor3 = Color3.new(1, 1, 1)
    toolsStatus.TextScaled = true
    toolsStatus.TextWrapped = true
    toolsStatus.Font = Enum.Font.SourceSans
    toolsStatus.Parent = toolsFrame
    
    return healthLabel, baseLabel, toolsStatus
end

-- Função para atualizar informações
local function updateUI(healthLabel, baseLabel, toolsStatus)
    if not player.Character then return end
    
    -- Atualiza vida
    local humanoid = player.Character:FindFirstChild("Humanoid")
    if humanoid then
        local health = math.floor(humanoid.Health)
        local maxHealth = math.floor(humanoid.MaxHealth)
        healthLabel.Text = "❤️ Vida: " .. health .. "/" .. maxHealth
    end
    
    -- Atualiza base
    local baseText = "🏠 Base: Nenhuma"
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if (owner and owner.Value == player) or (partner and partner.Value == player) then
                baseText = "🏠 Base: " .. base.Name
                break
            end
        end
    end
    baseLabel.Text = baseText
    
    -- Atualiza status das ferramentas
    local backpack = player:FindFirstChild("Backpack")
    local character = player.Character
    
    local hasCombatGun = false
    local hasCollectorGun = false
    
    if backpack then
        hasCombatGun = backpack:FindFirstChild("CombatGun") ~= nil
        hasCollectorGun = backpack:FindFirstChild("CollectorGun") ~= nil
    end
    
    if character then
        hasCombatGun = hasCombatGun or character:FindFirstChild("CombatGun") ~= nil
        hasCollectorGun = hasCollectorGun or character:FindFirstChild("CollectorGun") ~= nil
    end
    
    local combatStatus = hasCombatGun and "Disponível" or "❌ Não encontrada"
    local collectorStatus = hasCollectorGun and "Disponível" or "❌ Não encontrada"
    
    toolsStatus.Text = "🔫 CombatGun: " .. combatStatus .. "\n🔨 CollectorGun: " .. collectorStatus
end

-- Inicializa quando o jogador spawna
local function onCharacterAdded(character)
    wait(2)
    local healthLabel, baseLabel, toolsStatus = createAlwaysVisibleUI()
    
    -- Loop de atualização
    local connection
    connection = RunService.Heartbeat:Connect(function()
        if not player.Character then
            connection:Disconnect()
            return
        end
        updateUI(healthLabel, baseLabel, toolsStatus)
    end)
end

-- Conecta eventos
if player.Character then
    onCharacterAdded(player.Character)
end

player.CharacterAdded:Connect(onCharacterAdded)

print("✅ AlwaysVisibleUI carregado!")