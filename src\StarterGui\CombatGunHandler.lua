-- CombatGunHandler.lua
-- Script que gerencia a CombatGun

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Aguarda RemoteEvents
wait(2)
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local fireCombatGun = remoteEvents:WaitForChild("FireCombatGun")

-- Configurações
local DAMAGE = 25
local RANGE = 200
local FIRE_RATE = 0.5
local PROJECTILE_SPEED = 100

local lastFireTime = 0
local combatGun = nil
local isEquipped = false

-- Função para criar projétil visual
local function createProjectile(startPos, endPos)
    local projectile = Instance.new("Part")
    projectile.Name = "Projectile"
    projectile.Size = Vector3.new(0.5, 0.5, 2)
    projectile.BrickColor = BrickColor.new("Bright red")
    projectile.Material = Enum.Material.Neon
    projectile.CanCollide = false
    projectile.Anchored = true
    projectile.Parent = workspace
    
    local direction = (endPos - startPos).Unit
    projectile.CFrame = CFrame.lookAt(startPos, endPos)
    
    local distance = (endPos - startPos).Magnitude
    local duration = distance / PROJECTILE_SPEED
    
    local startTime = tick()
    local connection
    connection = RunService.Heartbeat:Connect(function()
        local elapsed = tick() - startTime
        local progress = elapsed / duration
        
        if progress >= 1 then
            connection:Disconnect()
            projectile:Destroy()
            return
        end
        
        local currentPos = startPos:Lerp(endPos, progress)
        projectile.CFrame = CFrame.lookAt(currentPos, currentPos + direction)
    end)
    
    -- Som
    local sound = Instance.new("Sound")
    sound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    sound.Volume = 0.5
    sound.Parent = projectile
    sound:Play()
    
    game:GetService("Debris"):AddItem(sound, 2)
end

-- Função de disparo
local function fire()
    if not isEquipped then return end
    
    local currentTime = tick()
    if currentTime - lastFireTime < FIRE_RATE then return end
    lastFireTime = currentTime
    
    local character = player.Character
    if not character then return end
    
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then return end
    
    local startPos = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 2
    local direction = (mouse.Hit.Position - startPos).Unit
    local endPos = startPos + direction * RANGE
    
    -- Raycast
    local raycastParams = RaycastParams.new()
    raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
    raycastParams.FilterDescendantsInstances = {character}
    
    local raycastResult = workspace:Raycast(startPos, direction * RANGE, raycastParams)
    
    if raycastResult then
        endPos = raycastResult.Position
        
        local hitCharacter = raycastResult.Instance.Parent
        local hitPlayer = Players:GetPlayerFromCharacter(hitCharacter)
        
        if hitPlayer and hitPlayer ~= player then
            fireCombatGun:FireServer(hitPlayer, DAMAGE, raycastResult.Position)
            print("🔫 Atirou em " .. hitPlayer.Name)
        end
    end
    
    createProjectile(startPos, endPos)
end

-- Função para conectar à ferramenta
local function connectToCombatGun()
    local function findTool()
        local backpack = player:WaitForChild("Backpack")
        local character = player.Character
        
        return backpack:FindFirstChild("CombatGun") or (character and character:FindFirstChild("CombatGun"))
    end
    
    local tool = findTool()
    if tool then
        tool.Equipped:Connect(function()
            isEquipped = true
            combatGun = tool
            mouse.Icon = "rbxasset://textures/GunCursor.png"
            print("🔫 CombatGun equipada!")
        end)
        
        tool.Unequipped:Connect(function()
            isEquipped = false
            combatGun = nil
            mouse.Icon = ""
            print("🔫 CombatGun desequipada!")
        end)
        
        tool.Activated:Connect(fire)
    else
        -- Aguarda a ferramenta aparecer
        local backpack = player:WaitForChild("Backpack")
        local character = player.Character
        
        local function onChildAdded(child)
            if child.Name == "CombatGun" and child:IsA("Tool") then
                child.Equipped:Connect(function()
                    isEquipped = true
                    combatGun = child
                    mouse.Icon = "rbxasset://textures/GunCursor.png"
                    print("🔫 CombatGun equipada!")
                end)
                
                child.Unequipped:Connect(function()
                    isEquipped = false
                    combatGun = nil
                    mouse.Icon = ""
                    print("🔫 CombatGun desequipada!")
                end)
                
                child.Activated:Connect(fire)
            end
        end
        
        backpack.ChildAdded:Connect(onChildAdded)
        if character then
            character.ChildAdded:Connect(onChildAdded)
        end
    end
end

-- Conecta quando o jogador spawna
if player.Character then
    connectToCombatGun()
end

player.CharacterAdded:Connect(function()
    wait(1)
    connectToCombatGun()
end)

print("🔫 CombatGun handler carregado!")