-- InviteUI.lua
-- Interface para sistema de convites

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local sendInvite = remoteEvents:WaitForChild("SendInvite")
local respondInvite = remoteEvents:WaitForChild("RespondInvite")
local updateInviteUI = remoteEvents:WaitForChild("UpdateInviteUI")

-- Variáveis da UI
local screenGui = nil
local inviteFrame = nil
local playerListFrame = nil
local notificationFrame = nil

-- Função para criar a interface principal
local function createInviteUI()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove UI existente
    local existingUI = playerGui:FindFirstChild("InviteUI")
    if existingUI then existingUI:Destroy() end
    
    -- Cria ScreenGui principal
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "InviteUI"
    screenGui.Parent = playerGui
    
    -- Botão principal de convite
    local inviteButton = Instance.new("TextButton")
    inviteButton.Name = "InviteButton"
    inviteButton.Size = UDim2.new(0, 120, 0, 40)
    inviteButton.Position = UDim2.new(1, -130, 0, 10)
    inviteButton.BackgroundColor3 = Color3.new(0, 0.7, 0)
    inviteButton.BorderSizePixel = 2
    inviteButton.BorderColor3 = Color3.new(1, 1, 1)
    inviteButton.Text = "CONVIDAR"
    inviteButton.TextColor3 = Color3.new(1, 1, 1)
    inviteButton.TextScaled = true
    inviteButton.Font = Enum.Font.SourceSansBold
    inviteButton.Parent = screenGui
    
    -- Frame da lista de jogadores
    playerListFrame = Instance.new("Frame")
    playerListFrame.Name = "PlayerListFrame"
    playerListFrame.Size = UDim2.new(0, 200, 0, 300)
    playerListFrame.Position = UDim2.new(1, -210, 0, 60)
    playerListFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    playerListFrame.BackgroundTransparency = 0.3
    playerListFrame.BorderSizePixel = 2
    playerListFrame.BorderColor3 = Color3.new(1, 1, 1)
    playerListFrame.Visible = false
    playerListFrame.Parent = screenGui
    
    -- Título da lista
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 30)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "Selecione um Jogador"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = playerListFrame
    
    -- ScrollingFrame para lista de jogadores
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 1, -40)
    scrollFrame.Position = UDim2.new(0, 5, 0, 35)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.BorderSizePixel = 0
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = playerListFrame
    
    -- Frame de notificações
    notificationFrame = Instance.new("Frame")
    notificationFrame.Name = "NotificationFrame"
    notificationFrame.Size = UDim2.new(0, 300, 0, 100)
    notificationFrame.Position = UDim2.new(0.5, -150, 0, 50)
    notificationFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    notificationFrame.BackgroundTransparency = 0.2
    notificationFrame.BorderSizePixel = 2
    notificationFrame.BorderColor3 = Color3.new(1, 1, 1)
    notificationFrame.Visible = false
    notificationFrame.Parent = screenGui
    
    return inviteButton, scrollFrame
end

-- Função para atualizar lista de jogadores
local function updatePlayerList(scrollFrame)
    -- Limpa lista existente
    for _, child in ipairs(scrollFrame:GetChildren()) do
        if child:IsA("TextButton") then
            child:Destroy()
        end
    end
    
    local yPosition = 0
    
    for _, otherPlayer in ipairs(Players:GetPlayers()) do
        if otherPlayer ~= player then
            local playerButton = Instance.new("TextButton")
            playerButton.Size = UDim2.new(1, -10, 0, 30)
            playerButton.Position = UDim2.new(0, 5, 0, yPosition)
            playerButton.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
            playerButton.BorderSizePixel = 1
            playerButton.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
            playerButton.Text = otherPlayer.Name
            playerButton.TextColor3 = Color3.new(1, 1, 1)
            playerButton.TextScaled = true
            playerButton.Font = Enum.Font.SourceSans
            playerButton.Parent = scrollFrame
            
            -- Conecta evento de clique
            playerButton.MouseButton1Click:Connect(function()
                sendInvite:FireServer(otherPlayer)
                playerListFrame.Visible = false
            end)
            
            yPosition = yPosition + 35
        end
    end
    
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPosition)
end

-- Função para mostrar notificação
local function showNotification(message, color, hasButtons)
    notificationFrame.Visible = true
    notificationFrame.BackgroundColor3 = color or Color3.new(0, 0, 0)
    
    -- Remove conteúdo anterior
    for _, child in ipairs(notificationFrame:GetChildren()) do
        child:Destroy()
    end
    
    -- Texto da notificação
    local messageLabel = Instance.new("TextLabel")
    messageLabel.Size = UDim2.new(1, 0, hasButtons and 0.6 or 1, 0)
    messageLabel.Position = UDim2.new(0, 0, 0, 0)
    messageLabel.BackgroundTransparency = 1
    messageLabel.Text = message
    messageLabel.TextColor3 = Color3.new(1, 1, 1)
    messageLabel.TextScaled = true
    messageLabel.TextWrapped = true
    messageLabel.Font = Enum.Font.SourceSansBold
    messageLabel.Parent = notificationFrame
    
    if hasButtons then
        -- Botão Aceitar
        local acceptButton = Instance.new("TextButton")
        acceptButton.Size = UDim2.new(0.4, 0, 0.3, 0)
        acceptButton.Position = UDim2.new(0.1, 0, 0.65, 0)
        acceptButton.BackgroundColor3 = Color3.new(0, 0.7, 0)
        acceptButton.BorderSizePixel = 1
        acceptButton.BorderColor3 = Color3.new(1, 1, 1)
        acceptButton.Text = "ACEITAR"
        acceptButton.TextColor3 = Color3.new(1, 1, 1)
        acceptButton.TextScaled = true
        acceptButton.Font = Enum.Font.SourceSansBold
        acceptButton.Parent = notificationFrame
        
        -- Botão Recusar
        local declineButton = Instance.new("TextButton")
        declineButton.Size = UDim2.new(0.4, 0, 0.3, 0)
        declineButton.Position = UDim2.new(0.5, 0, 0.65, 0)
        declineButton.BackgroundColor3 = Color3.new(0.7, 0, 0)
        declineButton.BorderSizePixel = 1
        declineButton.BorderColor3 = Color3.new(1, 1, 1)
        declineButton.Text = "RECUSAR"
        declineButton.TextColor3 = Color3.new(1, 1, 1)
        declineButton.TextScaled = true
        declineButton.Font = Enum.Font.SourceSansBold
        declineButton.Parent = notificationFrame
        
        -- Conecta eventos
        acceptButton.MouseButton1Click:Connect(function()
            respondInvite:FireServer(true)
            notificationFrame.Visible = false
        end)
        
        declineButton.MouseButton1Click:Connect(function()
            respondInvite:FireServer(false)
            notificationFrame.Visible = false
        end)
    else
        -- Auto-hide após 3 segundos
        spawn(function()
            wait(3)
            notificationFrame.Visible = false
        end)
    end
end

-- Inicializa a UI
local function initializeUI()
    local inviteButton, scrollFrame = createInviteUI()
    
    -- Conecta evento do botão principal
    inviteButton.MouseButton1Click:Connect(function()
        playerListFrame.Visible = not playerListFrame.Visible
        if playerListFrame.Visible then
            updatePlayerList(scrollFrame)
        end
    end)
    
    -- Conecta eventos de atualização
    updateInviteUI.OnClientEvent:Connect(function(eventType, playerName)
        if eventType == "received" then
            showNotification(playerName .. " convidou você para se juntar à equipe!", Color3.new(0, 0.5, 1), true)
        elseif eventType == "sent" then
            showNotification("Convite enviado para " .. playerName, Color3.new(0, 0.7, 0), false)
        elseif eventType == "accepted" then
            showNotification(playerName .. " aceitou seu convite!", Color3.new(0, 0.7, 0), false)
        elseif eventType == "declined" then
            showNotification(playerName .. " recusou seu convite.", Color3.new(0.7, 0.7, 0), false)
        elseif eventType == "joined" then
            showNotification("Você se juntou à equipe de " .. playerName .. "!", Color3.new(0, 0.7, 0), false)
        elseif eventType == "error" then
            showNotification(playerName, Color3.new(0.7, 0, 0), false)
        elseif eventType == "expired" then
            showNotification("Convite expirou.", Color3.new(0.7, 0.7, 0), false)
        elseif eventType == "cancelled" then
            showNotification("Convite cancelado.", Color3.new(0.7, 0.7, 0), false)
        end
    end)
end

-- Inicializa quando o jogador spawna
if player.Character then
    initializeUI()
end

player.CharacterAdded:Connect(function()
    wait(1)
    initializeUI()
end)

print("InviteUI carregado com sucesso!")