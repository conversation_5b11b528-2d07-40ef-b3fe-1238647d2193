-- GameInitializer.lua
-- Script principal para inicializar todo o jogo

local ServerStorage = game:GetService("ServerStorage")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local StarterPack = game:GetService("StarterPack")
local StarterGui = game:GetService("StarterGui")

print("🎮 === INICIALIZANDO JOGO DE ARENA - FASE 4 ===")

-- Função para executar script com tratamento de erro
local function executeScript(scriptName, scriptPath)
    local success, result = pcall(function()
        return require(scriptPath)
    end)
    
    if success then
        print("✅ " .. scriptName .. " - Sucesso")
        return true
    else
        warn("❌ " .. scriptName .. " - Erro: " .. tostring(result))
        return false
    end
end

-- 1. Cria RemoteEvents
print("1. Criando RemoteEvents...")
executeScript("RemoteEvents", ReplicatedStorage.RemoteEvents)

-- 2. Cria BaseTemplate
print("2. Criando BaseTemplate...")
executeScript("CreateBaseTemplate", ServerStorage.CreateBaseTemplate)

-- 3. Cria Ferramentas
print("3. Criando ferramentas...")
executeScript("CreateSimpleTools", ServerStorage.CreateSimpleTools)

-- Aguarda um pouco para garantir que tudo foi criado
wait(1)

-- 4. Inicializa ResourceManager
print("4. Inicializando ResourceManager...")
executeScript("ResourceManager", script.Parent.ResourceManager)

-- 5. Inicializa BaseManager
print("5. Inicializando BaseManager...")
executeScript("BaseManager", script.Parent.BaseManager)

-- 6. Inicializa CollectionManager
print("6. Inicializando CollectionManager...")
executeScript("CollectionManager", script.Parent.CollectionManager)

-- 7. Inicializa CombatManager
print("7. Inicializando CombatManager...")
executeScript("CombatManager", script.Parent.CombatManager)

-- 8. Inicializa DepositManager
print("8. Inicializando DepositManager...")
executeScript("DepositManager", script.Parent.DepositManager)

-- 9. Inicializa BaseAttackManager
print("9. Inicializando BaseAttackManager...")
executeScript("BaseAttackManager", script.Parent.BaseAttackManager)

-- 10. Inicializa InviteManager
print("10. Inicializando InviteManager...")
executeScript("InviteManager", script.Parent.InviteManager)

-- 11. Inicializa BuildingManager
print("11. Inicializando BuildingManager...")
executeScript("BuildingManager", script.Parent.BuildingManager)

-- 12. Inicializa SpawnManager
print("12. Inicializando SpawnManager...")
executeScript("SpawnManager", script.Parent.SpawnManager)

-- Aguarda mais um pouco
wait(1)

print("🎉 === JOGO INICIALIZADO COM SUCESSO - FASE 4 ===")
print("📋 FUNCIONALIDADES ATIVAS:")
print("• 8 bases reivindicáveis com cores únicas")
print("• Sistema de duplas com convites")
print("• Coleta de recursos com encolhimento")
print("• Combate PvP com respawn")
print("• Depósito automático de recursos")
print("• Ataque a bases inimigas")
print("• Efeitos de barreira (cura/dano)")
print("• Sistema de construção estratégica")
print("• Interface completa (HUD, convites, construção)")
print("• Condições de derrota e destruição")
print("")
print("🎮 COMO JOGAR:")
print("1. Toque em um ClaimPad amarelo para reivindicar uma base")
print("2. Use CombatGun (preta) para combate")
print("3. Use CollectorGun (azul) para coletar recursos e atacar bases")
print("4. Entre na sua barreira para depositar recursos automaticamente")
print("5. Use o botão CONVIDAR para formar duplas")
print("6. Pressione 'B' dentro da sua barreira para construir")
print("")
print("🏆 OBJETIVO: Sobreviva, colete recursos, construa defesas e destrua bases inimigas!")

return true