-- PlayerDataMenu.lua
-- Menu completo com dados do jogador

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer

-- Variáveis da UI
local screenGui = nil
local mainFrame = nil
local isMenuOpen = false

-- Dados do jogador
local playerData = {
    health = 100,
    maxHealth = 100,
    baseName = "Nenhuma",
    baseSize = 0,
    materials = 0,
    resourcesCollected = 0,
    enemiesKilled = 0,
    timePlayed = 0,
    inventory = {}
}

-- Função para criar o menu
local function createPlayerMenu()
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Remove menu existente
    local existingMenu = playerGui:FindFirstChild("PlayerDataMenu")
    if existingMenu then existingMenu:Destroy() end
    
    -- Cria ScreenGui
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "PlayerDataMenu"
    screenGui.Parent = playerGui
    
    -- Frame principal (inicialmente invisível)
    mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 400, 0, 500)
    mainFrame.Position = UDim2.new(0.5, -200, 0.5, -250)
    mainFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    mainFrame.BackgroundTransparency = 0.2
    mainFrame.BorderSizePixel = 3
    mainFrame.BorderColor3 = Color3.new(1, 1, 1)
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 40)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    titleLabel.BorderSizePixel = 0
    titleLabel.Text = "📊 DADOS DO JOGADOR"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame
    
    -- === SEÇÃO VIDA ===
    local healthSection = Instance.new("Frame")
    healthSection.Size = UDim2.new(1, -20, 0, 60)
    healthSection.Position = UDim2.new(0, 10, 0, 50)
    healthSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    healthSection.BorderSizePixel = 1
    healthSection.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    healthSection.Parent = mainFrame
    
    local healthTitle = Instance.new("TextLabel")
    healthTitle.Size = UDim2.new(1, 0, 0, 20)
    healthTitle.Position = UDim2.new(0, 0, 0, 5)
    healthTitle.BackgroundTransparency = 1
    healthTitle.Text = "❤️ VIDA"
    healthTitle.TextColor3 = Color3.new(1, 0, 0)
    healthTitle.TextScaled = true
    healthTitle.Font = Enum.Font.SourceSansBold
    healthTitle.Parent = healthSection
    
    local healthBar = Instance.new("Frame")
    healthBar.Size = UDim2.new(1, -10, 0, 15)
    healthBar.Position = UDim2.new(0, 5, 0, 25)
    healthBar.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    healthBar.BorderSizePixel = 1
    healthBar.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    healthBar.Parent = healthSection
    
    local healthFill = Instance.new("Frame")
    healthFill.Name = "HealthFill"
    healthFill.Size = UDim2.new(1, 0, 1, 0)
    healthFill.Position = UDim2.new(0, 0, 0, 0)
    healthFill.BackgroundColor3 = Color3.new(1, 0, 0)
    healthFill.BorderSizePixel = 0
    healthFill.Parent = healthBar
    
    local healthText = Instance.new("TextLabel")
    healthText.Name = "HealthText"
    healthText.Size = UDim2.new(1, 0, 1, 0)
    healthText.Position = UDim2.new(0, 0, 0, 0)
    healthText.BackgroundTransparency = 1
    healthText.Text = "100/100"
    healthText.TextColor3 = Color3.new(1, 1, 1)
    healthText.TextScaled = true
    healthText.Font = Enum.Font.SourceSansBold
    healthText.Parent = healthBar
    
    -- === SEÇÃO BASE ===
    local baseSection = Instance.new("Frame")
    baseSection.Size = UDim2.new(1, -20, 0, 80)
    baseSection.Position = UDim2.new(0, 10, 0, 120)
    baseSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    baseSection.BorderSizePixel = 1
    baseSection.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    baseSection.Parent = mainFrame
    
    local baseTitle = Instance.new("TextLabel")
    baseTitle.Size = UDim2.new(1, 0, 0, 20)
    baseTitle.Position = UDim2.new(0, 0, 0, 5)
    baseTitle.BackgroundTransparency = 1
    baseTitle.Text = "🏠 BASE"
    baseTitle.TextColor3 = Color3.new(0, 1, 0)
    baseTitle.TextScaled = true
    baseTitle.Font = Enum.Font.SourceSansBold
    baseTitle.Parent = baseSection
    
    local baseNameLabel = Instance.new("TextLabel")
    baseNameLabel.Name = "BaseNameLabel"
    baseNameLabel.Size = UDim2.new(1, 0, 0, 20)
    baseNameLabel.Position = UDim2.new(0, 0, 0, 25)
    baseNameLabel.BackgroundTransparency = 1
    baseNameLabel.Text = "Nome: Nenhuma"
    baseNameLabel.TextColor3 = Color3.new(1, 1, 1)
    baseNameLabel.TextScaled = true
    baseNameLabel.Font = Enum.Font.SourceSans
    baseNameLabel.Parent = baseSection
    
    local baseSizeLabel = Instance.new("TextLabel")
    baseSizeLabel.Name = "BaseSizeLabel"
    baseSizeLabel.Size = UDim2.new(1, 0, 0, 20)
    baseSizeLabel.Position = UDim2.new(0, 0, 0, 45)
    baseSizeLabel.BackgroundTransparency = 1
    baseSizeLabel.Text = "Tamanho: 0/500"
    baseSizeLabel.TextColor3 = Color3.new(1, 1, 1)
    baseSizeLabel.TextScaled = true
    baseSizeLabel.Font = Enum.Font.SourceSans
    baseSizeLabel.Parent = baseSection
    
    local materialsLabel = Instance.new("TextLabel")
    materialsLabel.Name = "MaterialsLabel"
    materialsLabel.Size = UDim2.new(1, 0, 0, 20)
    materialsLabel.Position = UDim2.new(0, 0, 0, 65)
    materialsLabel.BackgroundTransparency = 1
    materialsLabel.Text = "🔧 Materiais: 0"
    materialsLabel.TextColor3 = Color3.new(1, 1, 1)
    materialsLabel.TextScaled = true
    materialsLabel.Font = Enum.Font.SourceSans
    materialsLabel.Parent = baseSection
    
    -- === SEÇÃO CONQUISTAS ===
    local achievementsSection = Instance.new("Frame")
    achievementsSection.Size = UDim2.new(1, -20, 0, 100)
    achievementsSection.Position = UDim2.new(0, 10, 0, 210)
    achievementsSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    achievementsSection.BorderSizePixel = 1
    achievementsSection.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    achievementsSection.Parent = mainFrame
    
    local achievementsTitle = Instance.new("TextLabel")
    achievementsTitle.Size = UDim2.new(1, 0, 0, 20)
    achievementsTitle.Position = UDim2.new(0, 0, 0, 5)
    achievementsTitle.BackgroundTransparency = 1
    achievementsTitle.Text = "🏆 CONQUISTAS"
    achievementsTitle.TextColor3 = Color3.new(1, 1, 0)
    achievementsTitle.TextScaled = true
    achievementsTitle.Font = Enum.Font.SourceSansBold
    achievementsTitle.Parent = achievementsSection
    
    local resourcesLabel = Instance.new("TextLabel")
    resourcesLabel.Name = "ResourcesLabel"
    resourcesLabel.Size = UDim2.new(1, 0, 0, 20)
    resourcesLabel.Position = UDim2.new(0, 0, 0, 25)
    resourcesLabel.BackgroundTransparency = 1
    resourcesLabel.Text = "📦 Recursos Coletados: 0"
    resourcesLabel.TextColor3 = Color3.new(1, 1, 1)
    resourcesLabel.TextScaled = true
    resourcesLabel.Font = Enum.Font.SourceSans
    resourcesLabel.Parent = achievementsSection
    
    local killsLabel = Instance.new("TextLabel")
    killsLabel.Name = "KillsLabel"
    killsLabel.Size = UDim2.new(1, 0, 0, 20)
    killsLabel.Position = UDim2.new(0, 0, 0, 45)
    killsLabel.BackgroundTransparency = 1
    killsLabel.Text = "⚔️ Inimigos Eliminados: 0"
    killsLabel.TextColor3 = Color3.new(1, 1, 1)
    killsLabel.TextScaled = true
    killsLabel.Font = Enum.Font.SourceSans
    killsLabel.Parent = achievementsSection
    
    local timeLabel = Instance.new("TextLabel")
    timeLabel.Name = "TimeLabel"
    timeLabel.Size = UDim2.new(1, 0, 0, 20)
    timeLabel.Position = UDim2.new(0, 0, 0, 65)
    timeLabel.BackgroundTransparency = 1
    timeLabel.Text = "⏰ Tempo Jogado: 0:00"
    timeLabel.TextColor3 = Color3.new(1, 1, 1)
    timeLabel.TextScaled = true
    timeLabel.Font = Enum.Font.SourceSans
    timeLabel.Parent = achievementsSection
    
    -- === SEÇÃO MOCHILA ===
    local inventorySection = Instance.new("Frame")
    inventorySection.Size = UDim2.new(1, -20, 0, 100)
    inventorySection.Position = UDim2.new(0, 10, 0, 320)
    inventorySection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    inventorySection.BorderSizePixel = 1
    inventorySection.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    inventorySection.Parent = mainFrame
    
    local inventoryTitle = Instance.new("TextLabel")
    inventoryTitle.Size = UDim2.new(1, 0, 0, 20)
    inventoryTitle.Position = UDim2.new(0, 0, 0, 5)
    inventoryTitle.BackgroundTransparency = 1
    inventoryTitle.Text = "🎒 MOCHILA"
    inventoryTitle.TextColor3 = Color3.new(0, 1, 1)
    inventoryTitle.TextScaled = true
    inventoryTitle.Font = Enum.Font.SourceSansBold
    inventoryTitle.Parent = inventorySection
    
    local inventoryText = Instance.new("TextLabel")
    inventoryText.Name = "InventoryText"
    inventoryText.Size = UDim2.new(1, 0, 1, -25)
    inventoryText.Position = UDim2.new(0, 0, 0, 25)
    inventoryText.BackgroundTransparency = 1
    inventoryText.Text = "🔫 CombatGun\n🔨 CollectorGun\n💎 Recursos carregados: 0"
    inventoryText.TextColor3 = Color3.new(1, 1, 1)
    inventoryText.TextScaled = true
    inventoryText.TextWrapped = true
    inventoryText.Font = Enum.Font.SourceSans
    inventoryText.Parent = inventorySection
    
    -- === INSTRUÇÕES ===
    local instructionsSection = Instance.new("Frame")
    instructionsSection.Size = UDim2.new(1, -20, 0, 60)
    instructionsSection.Position = UDim2.new(0, 10, 0, 430)
    instructionsSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    instructionsSection.BorderSizePixel = 1
    instructionsSection.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    instructionsSection.Parent = mainFrame
    
    local instructionsLabel = Instance.new("TextLabel")
    instructionsLabel.Size = UDim2.new(1, 0, 1, 0)
    instructionsLabel.Position = UDim2.new(0, 0, 0, 0)
    instructionsLabel.BackgroundTransparency = 1
    instructionsLabel.Text = "Pressione TAB para abrir/fechar este menu\nToque ClaimPad para reivindicar base\nUse ferramentas para combate e coleta"
    instructionsLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    instructionsLabel.TextScaled = true
    instructionsLabel.TextWrapped = true
    instructionsLabel.Font = Enum.Font.SourceSans
    instructionsLabel.Parent = instructionsSection
    
    -- Botão fechar
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.new(0.7, 0, 0)
    closeButton.BorderSizePixel = 1
    closeButton.BorderColor3 = Color3.new(1, 1, 1)
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame
    
    closeButton.MouseButton1Click:Connect(function()
        toggleMenu()
    end)
    
    return mainFrame
end

-- Função para alternar menu
local function toggleMenu()
    if mainFrame then
        isMenuOpen = not isMenuOpen
        mainFrame.Visible = isMenuOpen
    end
end

-- Função para atualizar dados
local function updatePlayerData()
    if not player.Character then return end
    
    -- Atualiza vida
    local humanoid = player.Character:FindFirstChild("Humanoid")
    if humanoid then
        playerData.health = math.floor(humanoid.Health)
        playerData.maxHealth = math.floor(humanoid.MaxHealth)
    end
    
    -- Atualiza base
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if (owner and owner.Value == player) or (partner and partner.Value == player) then
                playerData.baseName = base.Name
                local baseSizeValue = base:FindFirstChild("BaseSize")
                local materialsValue = base:FindFirstChild("BuildingMaterials")
                
                if baseSizeValue then playerData.baseSize = math.floor(baseSizeValue.Value) end
                if materialsValue then playerData.materials = materialsValue.Value end
                break
            end
        end
    end
    
    -- Verifica se está carregando recurso
    local carryingResource = 0
    if player.Character and player.Character:FindFirstChild("CarregandoRecurso") then
        local resourceValue = player.Character:FindFirstChild("ResourceValue")
        if resourceValue then
            carryingResource = resourceValue.Value
        end
    end
    
    -- Atualiza tempo jogado
    playerData.timePlayed = playerData.timePlayed + 0.1
    
    -- Atualiza UI se estiver visível
    if mainFrame and mainFrame.Visible then
        local healthFill = mainFrame:FindFirstChild("MainFrame"):FindFirstChild("HealthFill")
        local healthText = mainFrame:FindFirstChild("MainFrame"):FindFirstChild("HealthText")
        
        if healthFill and healthText then
            local healthRatio = playerData.health / playerData.maxHealth
            healthFill.Size = UDim2.new(healthRatio, 0, 1, 0)
            healthText.Text = playerData.health .. "/" .. playerData.maxHealth
        end
        
        local baseNameLabel = mainFrame:FindFirstChild("BaseNameLabel")
        local baseSizeLabel = mainFrame:FindFirstChild("BaseSizeLabel")
        local materialsLabel = mainFrame:FindFirstChild("MaterialsLabel")
        local resourcesLabel = mainFrame:FindFirstChild("ResourcesLabel")
        local timeLabel = mainFrame:FindFirstChild("TimeLabel")
        local inventoryText = mainFrame:FindFirstChild("InventoryText")
        
        if baseNameLabel then baseNameLabel.Text = "Nome: " .. playerData.baseName end
        if baseSizeLabel then baseSizeLabel.Text = "Tamanho: " .. playerData.baseSize .. "/500" end
        if materialsLabel then materialsLabel.Text = "🔧 Materiais: " .. playerData.materials end
        if resourcesLabel then resourcesLabel.Text = "📦 Recursos Coletados: " .. playerData.resourcesCollected end
        if timeLabel then 
            local minutes = math.floor(playerData.timePlayed / 60)
            local seconds = math.floor(playerData.timePlayed % 60)
            timeLabel.Text = "⏰ Tempo Jogado: " .. minutes .. ":" .. string.format("%02d", seconds)
        end
        if inventoryText then 
            inventoryText.Text = "🔫 CombatGun\n🔨 CollectorGun\n💎 Recursos carregados: " .. carryingResource
        end
    end
end

-- Conecta input para abrir menu
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.Tab then
        toggleMenu()
    end
end)

-- Inicializa quando o jogador spawna
local function onCharacterAdded(character)
    wait(2)
    createPlayerMenu()
    
    -- Loop de atualização
    local connection
    connection = RunService.Heartbeat:Connect(function()
        if not player.Character then
            connection:Disconnect()
            return
        end
        updatePlayerData()
    end)
end

-- Conecta eventos
if player.Character then
    onCharacterAdded(player.Character)
end

player.CharacterAdded:Connect(onCharacterAdded)

print("📊 PlayerDataMenu carregado! Pressione TAB para abrir.")