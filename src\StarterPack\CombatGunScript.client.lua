-- CombatGunScript.client.lua
-- LocalScript para a CombatGun

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local fireCombatGun = remoteEvents:WaitForChild("FireCombatGun")

-- Configurações da arma
local DAMAGE = 25
local RANGE = 200
local FIRE_RATE = 0.5
local PROJECTILE_SPEED = 100

-- Controle de disparo
local lastFireTime = 0
local tool = script.Parent

-- Função para criar efeito visual do projétil
local function createProjectileEffect(startPos, endPos)
    local projectile = Instance.new("Part")
    projectile.Name = "Projectile"
    projectile.Size = Vector3.new(0.5, 0.5, 2)
    projectile.BrickColor = BrickColor.new("Bright red")
    projectile.Material = Enum.Material.Neon
    projectile.CanCollide = false
    projectile.Anchored = true
    projectile.Parent = workspace
    
    -- Posiciona o projétil
    local direction = (endPos - startPos).Unit
    projectile.CFrame = CFrame.lookAt(startPos, endPos)
    
    -- Efeito de movimento
    local distance = (endPos - startPos).Magnitude
    local duration = distance / PROJECTILE_SPEED
    
    local startTime = tick()
    local connection
    connection = RunService.Heartbeat:Connect(function()
        local elapsed = tick() - startTime
        local progress = elapsed / duration
        
        if progress >= 1 then
            connection:Disconnect()
            projectile:Destroy()
            return
        end
        
        local currentPos = startPos:Lerp(endPos, progress)
        projectile.CFrame = CFrame.lookAt(currentPos, currentPos + direction)
    end)
    
    -- Som do disparo
    local sound = Instance.new("Sound")
    sound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    sound.Volume = 0.5
    sound.Pitch = 1.2
    sound.Parent = projectile
    sound:Play()
    
    game:GetService("Debris"):AddItem(sound, 2)
end

-- Função para disparar
local function fire()
    local currentTime = tick()
    if currentTime - lastFireTime < FIRE_RATE then
        return
    end
    
    lastFireTime = currentTime
    
    -- Raycast para detectar alvo
    local character = player.Character
    if not character then return end
    
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then return end
    
    -- Posição de origem (da ferramenta)
    local startPos = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 2
    local direction = (mouse.Hit.Position - startPos).Unit
    local endPos = startPos + direction * RANGE
    
    -- Raycast
    local raycastParams = RaycastParams.new()
    raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
    raycastParams.FilterDescendantsInstances = {character}
    
    local raycastResult = workspace:Raycast(startPos, direction * RANGE, raycastParams)
    
    if raycastResult then
        endPos = raycastResult.Position
        
        -- Verifica se atingiu um jogador
        local hitCharacter = raycastResult.Instance.Parent
        local hitHumanoid = hitCharacter:FindFirstChild("Humanoid")
        local hitPlayer = Players:GetPlayerFromCharacter(hitCharacter)
        
        if hitPlayer and hitPlayer ~= player then
            -- Envia evento para o servidor
            fireCombatGun:FireServer(hitPlayer, DAMAGE, raycastResult.Position)
        end
    end
    
    -- Cria efeito visual
    createProjectileEffect(startPos, endPos)
end

-- Conecta eventos da ferramenta
tool.Activated:Connect(fire)

tool.Equipped:Connect(function()
    mouse.Icon = "rbxasset://textures/GunCursor.png"
end)

tool.Unequipped:Connect(function()
    mouse.Icon = ""
end)