-- SpawnManager.lua
-- Gerencia spawn dos jogadores em bases não reivindicadas

local Players = game:GetService("Players")

local SpawnManager = {}

-- Função para encontrar uma base não reivindicada
local function findUnclaimedBase()
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            if owner and not owner.Value then
                return base
            end
        end
    end
    return nil
end

-- Função para criar spawn temporário perto de uma base
local function createTempSpawn(base)
    local spawnLocation = Instance.new("SpawnLocation")
    spawnLocation.Name = "TempSpawn_" .. base.Name
    spawnLocation.Size = Vector3.new(6, 1, 6)
    spawnLocation.BrickColor = BrickColor.new("Bright green")
    spawnLocation.Material = Enum.Material.Neon
    spawnLocation.Anchored = true
    spawnLocation.Parent = workspace
    
    -- Posiciona o spawn na frente da base
    local basePosition = base.PrimaryPart and base.PrimaryPart.Position or Vector3.new(0, 0, 0)
    spawnLocation.Position = basePosition + Vector3.new(0, 1, 20)
    
    -- Adiciona texto
    local surfaceGui = Instance.new("SurfaceGui")
    surfaceGui.Face = Enum.NormalId.Top
    surfaceGui.Parent = spawnLocation
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "SPAWN TEMPORÁRIO"
    textLabel.TextColor3 = Color3.new(1, 1, 1)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = surfaceGui
    
    return spawnLocation
end

-- Função para configurar spawn do jogador
local function setupPlayerSpawn(player)
    -- Encontra uma base não reivindicada
    local unclaimedBase = findUnclaimedBase()
    
    if unclaimedBase then
        -- Cria spawn temporário perto da base
        local tempSpawn = createTempSpawn(unclaimedBase)
        player.RespawnLocation = tempSpawn
        
        print("Jogador " .. player.Name .. " spawnará perto da " .. unclaimedBase.Name)
        
        -- Remove spawn temporário após 60 segundos
        spawn(function()
            wait(60)
            if tempSpawn and tempSpawn.Parent then
                tempSpawn:Destroy()
            end
        end)
    else
        -- Se não há bases disponíveis, usa spawn central
        local centralSpawn = workspace:FindFirstChild("CentralSpawn")
        if centralSpawn then
            player.RespawnLocation = centralSpawn
            print("Jogador " .. player.Name .. " spawnará no centro (sem bases disponíveis)")
        end
    end
end

-- Conecta aos novos jogadores
Players.PlayerAdded:Connect(function(player)
    setupPlayerSpawn(player)
    
    -- Redefine spawn quando o jogador spawna
    player.CharacterAdded:Connect(function(character)
        -- Se o jogador não tem base, configura spawn temporário
        local hasBase = false
        for _, base in ipairs(workspace:GetChildren()) do
            if base.Name:match("Base_") then
                local owner = base:FindFirstChild("Owner")
                local partner = base:FindFirstChild("Partner")
                
                if (owner and owner.Value == player) or (partner and partner.Value == player) then
                    hasBase = true
                    break
                end
            end
        end
        
        if not hasBase then
            setupPlayerSpawn(player)
        end
    end)
end)

-- Configura spawn para jogadores já conectados
for _, player in ipairs(Players:GetPlayers()) do
    setupPlayerSpawn(player)
end

print("SpawnManager inicializado - jogadores spawnarão perto de bases não reivindicadas!")

return SpawnManager