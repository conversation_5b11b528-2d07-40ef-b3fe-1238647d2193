-- CollectorGunHandler.lua
-- Script que gerencia a CollectorGun

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Aguarda RemoteEvents
wait(2)
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local startCollecting = remoteEvents:WaitForChild("StartCollecting")
local stopCollecting = remoteEvents:WaitForChild("StopCollecting")

-- Configurações
local RANGE = 150
local isCollecting = false
local isEquipped = false
local currentTarget = nil
local collectConnection = nil
local collectorGun = nil

-- Função para criar raio visual
local function createBeam(startPos, endPos, color)
    local beam = Instance.new("Part")
    beam.Name = "CollectorBeam"
    beam.Size = Vector3.new(0.2, 0.2, (endPos - startPos).Magnitude)
    beam.BrickColor = color or BrickColor.new("Bright blue")
    beam.Material = Enum.Material.Neon
    beam.CanCollide = false
    beam.Anchored = true
    beam.Parent = workspace
    
    beam.CFrame = CFrame.lookAt(startPos, endPos) * CFrame.new(0, 0, -beam.Size.Z / 2)
    
    local transparency = 0
    local connection
    connection = RunService.Heartbeat:Connect(function()
        transparency = transparency + 0.1
        beam.Transparency = transparency
        
        if transparency >= 1 then
            connection:Disconnect()
            beam:Destroy()
        end
    end)
end

-- Função para verificar se é coletável
local function isCollectable(obj)
    return obj:FindFirstChild("OriginalSize") and obj.Parent == workspace
end

-- Função para iniciar coleta
local function startCollectingResource()
    if not isEquipped then return end
    
    isCollecting = true
    print("🔨 Iniciando coleta...")
    
    collectConnection = RunService.Heartbeat:Connect(function()
        if not isCollecting then
            collectConnection:Disconnect()
            return
        end
        
        local character = player.Character
        if not character then return end
        
        local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
        if not humanoidRootPart then return end
        
        local startPos = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 2
        local direction = (mouse.Hit.Position - startPos).Unit
        
        -- Raycast
        local raycastParams = RaycastParams.new()
        raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
        raycastParams.FilterDescendantsInstances = {character}
        
        local raycastResult = workspace:Raycast(startPos, direction * RANGE, raycastParams)
        
        if raycastResult then
            local hitObject = raycastResult.Instance
            local endPos = raycastResult.Position
            
            if isCollectable(hitObject) then
                currentTarget = hitObject
                createBeam(startPos, endPos, BrickColor.new("Bright blue"))
                startCollecting:FireServer(hitObject)
            else
                currentTarget = nil
            end
        else
            currentTarget = nil
        end
    end)
end

-- Função para parar coleta
local function stopCollectingResource()
    isCollecting = false
    print("🔨 Parando coleta...")
    
    if collectConnection then
        collectConnection:Disconnect()
        collectConnection = nil
    end
    
    if currentTarget and isCollectable(currentTarget) then
        stopCollecting:FireServer(currentTarget)
    end
    
    currentTarget = nil
end

-- Função para conectar à ferramenta
local function connectToCollectorGun()
    local function findTool()
        local backpack = player:WaitForChild("Backpack")
        local character = player.Character
        
        return backpack:FindFirstChild("CollectorGun") or (character and character:FindFirstChild("CollectorGun"))
    end
    
    local tool = findTool()
    if tool then
        tool.Equipped:Connect(function()
            isEquipped = true
            collectorGun = tool
            mouse.Icon = "rbxasset://textures/ArrowCursor.png"
            print("🔨 CollectorGun equipada!")
        end)
        
        tool.Unequipped:Connect(function()
            isEquipped = false
            collectorGun = nil
            stopCollectingResource()
            mouse.Icon = ""
            print("🔨 CollectorGun desequipada!")
        end)
        
        tool.Activated:Connect(startCollectingResource)
        tool.Deactivated:Connect(stopCollectingResource)
    else
        -- Aguarda a ferramenta aparecer
        local backpack = player:WaitForChild("Backpack")
        local character = player.Character
        
        local function onChildAdded(child)
            if child.Name == "CollectorGun" and child:IsA("Tool") then
                child.Equipped:Connect(function()
                    isEquipped = true
                    collectorGun = child
                    mouse.Icon = "rbxasset://textures/ArrowCursor.png"
                    print("🔨 CollectorGun equipada!")
                end)
                
                child.Unequipped:Connect(function()
                    isEquipped = false
                    collectorGun = nil
                    stopCollectingResource()
                    mouse.Icon = ""
                    print("🔨 CollectorGun desequipada!")
                end)
                
                child.Activated:Connect(startCollectingResource)
                child.Deactivated:Connect(stopCollectingResource)
            end
        end
        
        backpack.ChildAdded:Connect(onChildAdded)
        if character then
            character.ChildAdded:Connect(onChildAdded)
        end
    end
end

-- Conecta quando o jogador spawna
if player.Character then
    connectToCollectorGun()
end

player.CharacterAdded:Connect(function()
    wait(1)
    connectToCollectorGun()
end)

print("🔨 CollectorGun handler carregado!")