-- CombatManager.lua
-- Gerencia o combate PvP no servidor

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Aguarda dependências
local remoteEvents = ReplicatedStorage:WaitF<PERSON><PERSON>hild("RemoteEvents")
local fireCombatGun = remoteEvents:Wait<PERSON><PERSON><PERSON>hild("FireCombatGun")
local playerDamaged = remoteEvents:WaitForChild("PlayerDamaged")

local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))
local BaseController = require(script.Parent.BaseController)

local CombatManager = {}

-- Cache de bases dos jogadores
local playerBases = {}

-- Função para obter a base de um jogador
local function getPlayerBase(player)
    if playerBases[player] then
        return playerBases[player]
    end
    
    -- Procura a base do jogador
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:Find<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if owner and (owner.Value == player or (partner and partner.Value == player)) then
                playerBases[player] = base
                return base
            end
        end
    end
    
    return nil
end

-- Função para verificar se dois jogadores são da mesma equipe
local function areTeammates(player1, player2)
    if player1 == player2 then return true end
    
    local base1 = getPlayerBase(player1)
    local base2 = getPlayerBase(player2)
    
    return base1 and base2 and base1 == base2
end

-- Função para aplicar dano a um jogador
local function damagePlayer(victim, attacker, damage)
    if not victim.Character or not victim.Character:FindFirstChild("Humanoid") then
        return false
    end
    
    local humanoid = victim.Character.Humanoid
    
    -- Verifica se são da mesma equipe
    if areTeammates(victim, attacker) then
        return false -- Não pode atacar companheiros de equipe
    end
    
    -- Aplica dano
    humanoid.Health = humanoid.Health - damage
    
    -- Notifica o cliente sobre o dano
    playerDamaged:FireClient(victim, damage, attacker.Name)
    
    print(attacker.Name .. " causou " .. damage .. " de dano a " .. victim.Name)
    
    return true
end

-- Manipula disparos de arma
fireCombatGun.OnServerEvent:Connect(function(player, victim, damage, hitPosition)
    if not victim or not victim.Character or not victim.Character:FindFirstChild("Humanoid") then
        return
    end
    
    -- Verifica distância
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        local distance = (player.Character.HumanoidRootPart.Position - victim.Character.HumanoidRootPart.Position).Magnitude
        if distance > GameConfig.COMBAT_CONFIG.WEAPON_RANGE then
            return
        end
    end
    
    -- Aplica dano
    if damagePlayer(victim, player, damage) then
        -- Efeito visual no local do impacto
        local explosion = Instance.new("Explosion")
        explosion.Position = hitPosition
        explosion.BlastRadius = 5
        explosion.BlastPressure = 0
        explosion.Parent = workspace
    end
end)

-- Limpa cache quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    playerBases[player] = nil
end)

print("CombatManager inicializado com sucesso!")

return CombatManager