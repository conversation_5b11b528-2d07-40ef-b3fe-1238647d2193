-- BuildingManager.lua
-- Gerencia o sistema de construção estratégica

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Aguarda dependências
local remoteEvents = ReplicatedStorage:WaitFor<PERSON>hild("RemoteEvents")
local requestBuild = remoteEvents:WaitFor<PERSON>hild("RequestBuild")
local buildResponse = remoteEvents:WaitForChild("BuildResponse")

local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))
local BaseController = require(script.Parent.BaseController)

local BuildingManager = {}

-- Tipos de construções
local BUILDING_TYPES = {
    Wall = {
        name = "Muro de Pedra",
        cost = GameConfig.BUILDING_CONFIG.WALL_COST,
        size = Vector3.new(8, 6, 2),
        color = BrickColor.new("Dark stone grey"),
        material = Enum.Material.Rock
    },
    Tower = {
        name = "Torre de Vigia",
        cost = GameConfig.BUILDING_CONFIG.TOWER_COST,
        size = Vector3.new(4, 12, 4),
        color = BrickColor.new("Brown"),
        material = Enum.Material.Wood
    },
    Generator = {
        name = "Gerador de Barreira",
        cost = GameConfig.BUILDING_CONFIG.GENERATOR_COST,
        size = Vector3.new(6, 4, 6),
        color = BrickColor.new("Bright blue"),
        material = Enum.Material.Neon
    },
    Depot = {
        name = "Depósito de Recursos",
        cost = GameConfig.BUILDING_CONFIG.DEPOT_COST,
        size = Vector3.new(6, 6, 6),
        color = BrickColor.new("Bright yellow"),
        material = Enum.Material.Metal
    }
}

-- Função para obter a base de um jogador
local function getPlayerBase(player)
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if owner and (owner.Value == player or (partner and partner.Value == player)) then
                return base
            end
        end
    end
    return nil
end

-- Função para verificar se uma posição é válida para construção
local function isValidBuildPosition(base, position, buildingSize)
    -- Verifica se está dentro da barreira
    if not BaseController.IsPlayerInBarrier({Character = {HumanoidRootPart = {Position = position}}}, base) then
        return false, "Deve construir dentro da barreira da base!"
    end
    
    -- Verifica dist��ncia da torre central
    local coreTower = base:FindFirstChild("CoreTower")
    if coreTower then
        local distance = (position - coreTower.Position).Magnitude
        if distance < 8 then
            return false, "Muito próximo da torre central!"
        end
    end
    
    -- Verifica se não há outras construções muito próximas
    for _, obj in ipairs(workspace:GetChildren()) do
        if obj:FindFirstChild("BuildingOwner") and obj.BuildingOwner.Value == base then
            local distance = (position - obj.Position).Magnitude
            if distance < (buildingSize.X + obj.Size.X) / 2 + 2 then
                return false, "Muito próximo de outra construção!"
            end
        end
    end
    
    return true, ""
end

-- Função para criar uma construção
local function createBuilding(buildingType, position, base)
    local building = Instance.new("Part")
    building.Name = buildingType.name
    building.Size = buildingType.size
    building.Position = position
    building.BrickColor = buildingType.color
    building.Material = buildingType.material
    building.Anchored = true
    building.Parent = workspace
    
    -- Marca como construção desta base
    local buildingOwner = Instance.new("ObjectValue")
    buildingOwner.Name = "BuildingOwner"
    buildingOwner.Value = base
    buildingOwner.Parent = building
    
    -- Adiciona funcionalidade especial baseada no tipo
    if buildingType == BUILDING_TYPES.Tower then
        -- Torre de vigia - adiciona luz
        local light = Instance.new("PointLight")
        light.Brightness = 2
        light.Range = 30
        light.Color = Color3.new(1, 1, 0.8)
        light.Parent = building
        
    elseif buildingType == BUILDING_TYPES.Generator then
        -- Gerador - efeito de partículas
        local attachment = Instance.new("Attachment")
        attachment.Parent = building
        
        local particles = Instance.new("ParticleEmitter")
        particles.Texture = "rbxasset://textures/particles/sparkles_main.dds"
        particles.Color = ColorSequence.new(Color3.new(0, 0.8, 1))
        particles.Size = NumberSequence.new(1)
        particles.Lifetime = NumberRange.new(1, 2)
        particles.Rate = 10
        particles.SpreadAngle = Vector2.new(45, 45)
        particles.Speed = NumberRange.new(2, 5)
        particles.Parent = attachment
        
    elseif buildingType == BUILDING_TYPES.Depot then
        -- Depósito - adiciona texto
        local surfaceGui = Instance.new("SurfaceGui")
        surfaceGui.Face = Enum.NormalId.Front
        surfaceGui.Parent = building
        
        local textLabel = Instance.new("TextLabel")
        textLabel.Size = UDim2.new(1, 0, 1, 0)
        textLabel.BackgroundTransparency = 1
        textLabel.Text = "DEPÓSITO"
        textLabel.TextColor3 = Color3.new(0, 0, 0)
        textLabel.TextScaled = true
        textLabel.Font = Enum.Font.SourceSansBold
        textLabel.Parent = surfaceGui
    end
    
    return building
end

-- Manipula solicitações de construção
requestBuild.OnServerEvent:Connect(function(player, buildingTypeName, position)
    local base = getPlayerBase(player)
    if not base then
        buildResponse:FireClient(player, false, "Você precisa ter uma base para construir!")
        return
    end
    
    -- Verifica se é o dono da base
    local owner = base:FindFirstChild("Owner")
    if not owner or owner.Value ~= player then
        buildResponse:FireClient(player, false, "Apenas o dono da base pode construir!")
        return
    end
    
    -- Verifica se o tipo de construção existe
    local buildingType = BUILDING_TYPES[buildingTypeName]
    if not buildingType then
        buildResponse:FireClient(player, false, "Tipo de construção inválido!")
        return
    end
    
    -- Verifica se tem materiais suficientes
    local buildingMaterialsValue = base:FindFirstChild("BuildingMaterials")
    if not buildingMaterialsValue or buildingMaterialsValue.Value < buildingType.cost then
        buildResponse:FireClient(player, false, "Materiais insuficientes! Necessário: " .. buildingType.cost)
        return
    end
    
    -- Verifica se a posição é válida
    local isValid, reason = isValidBuildPosition(base, position, buildingType.size)
    if not isValid then
        buildResponse:FireClient(player, false, reason)
        return
    end
    
    -- Deduz o custo
    buildingMaterialsValue.Value = buildingMaterialsValue.Value - buildingType.cost
    
    -- Cria a construção
    local building = createBuilding(buildingType, position, base)
    
    -- Resposta de sucesso
    buildResponse:FireClient(player, true, buildingType.name .. " construído com sucesso!")
    
    print(player.Name .. " construiu " .. buildingType.name .. " na base " .. base.Name)
end)

print("BuildingManager inicializado com sucesso!")

return BuildingManager